/**
 * Frontend JavaScript for DataAI Crawler
 */

// Global variables
let currentGames = [];
let loadingModal;
let gameDetailsModal;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    gameDetailsModal = new bootstrap.Modal(document.getElementById('gameDetailsModal'));
    
    // Load initial data
    checkHealth();
    loadAllGames();
});

/**
 * API helper functions
 */
async function apiCall(endpoint, options = {}) {
    try {
        const response = await fetch(`/api${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        logMessage(`API Error: ${error.message}`, 'error');
        throw error;
    }
}

/**
 * Logging functions
 */
function logMessage(message, level = 'info') {
    const logContainer = document.getElementById('log-container');
    const timestamp = new Date().toLocaleTimeString();
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    
    const levelIcon = {
        'info': 'fas fa-info-circle',
        'warn': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle',
        'success': 'fas fa-check-circle'
    }[level] || 'fas fa-info-circle';
    
    logEntry.innerHTML = `
        <span class="log-timestamp">[${timestamp}]</span>
        <i class="${levelIcon} me-1"></i>
        ${message}
    `;
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearLog() {
    document.getElementById('log-container').innerHTML = '';
    logMessage('Log cleared', 'info');
}

/**
 * Loading modal functions
 */
function showLoading(message = 'Processing...', details = 'Please wait while we process your request.') {
    document.getElementById('loading-message').textContent = message;
    document.getElementById('loading-details').textContent = details;
    loadingModal.show();
}

function hideLoading() {
    loadingModal.hide();
}

/**
 * Health check
 */
async function checkHealth() {
    try {
        const response = await fetch('/health');
        const data = await response.json();
        
        const statusBadge = document.getElementById('status-badge');
        
        if (data.status === 'OK') {
            statusBadge.className = 'badge bg-success me-2';
            statusBadge.textContent = 'Online';
            logMessage('Health check: System is online', 'success');
        } else {
            statusBadge.className = 'badge bg-danger me-2';
            statusBadge.textContent = 'Offline';
            logMessage('Health check: System is offline', 'error');
        }
    } catch (error) {
        const statusBadge = document.getElementById('status-badge');
        statusBadge.className = 'badge bg-danger me-2';
        statusBadge.textContent = 'Error';
        logMessage(`Health check failed: ${error.message}`, 'error');
    }
}

/**
 * Crawler functions
 */
async function initializeCrawler() {
    try {
        showLoading('Initializing Browser...', 'Starting up the browser engine for web crawling.');
        
        await apiCall('/crawler/init', { method: 'POST' });
        
        logMessage('Browser initialized successfully', 'success');
        hideLoading();
        
    } catch (error) {
        hideLoading();
        logMessage(`Failed to initialize browser: ${error.message}`, 'error');
    }
}

async function closeCrawler() {
    try {
        showLoading('Closing Browser...', 'Shutting down the browser engine.');
        
        await apiCall('/crawler/close', { method: 'POST' });
        
        logMessage('Browser closed successfully', 'success');
        hideLoading();
        
    } catch (error) {
        hideLoading();
        logMessage(`Failed to close browser: ${error.message}`, 'error');
    }
}

async function startCrawling() {
    const urlInput = document.getElementById('urlInput');
    const url = urlInput.value.trim();
    
    if (!url) {
        logMessage('Please enter a URL to crawl', 'warn');
        urlInput.focus();
        return;
    }
    
    if (!isValidUrl(url)) {
        logMessage('Please enter a valid URL', 'warn');
        urlInput.classList.add('is-invalid');
        return;
    }
    
    urlInput.classList.remove('is-invalid');
    urlInput.classList.add('is-valid');
    
    try {
        showLoading('Crawling Website...', `Extracting game data from ${new URL(url).hostname}`);
        
        const result = await apiCall('/crawl', {
            method: 'POST',
            body: JSON.stringify({ url })
        });
        
        hideLoading();

        if (result.success) {
            if (result.summary) {
                const { summary } = result;
                logMessage(`Crawling completed: ${summary.success} games added, ${summary.errors} errors`, 'success');
            } else if (result.count === 0) {
                logMessage(`Crawling completed: No games found at the provided URL`, 'warn');
            } else {
                logMessage(`Crawling completed successfully`, 'success');
            }

            // Refresh the games list
            await loadAllGames();
        } else {
            logMessage(`Crawling failed: ${result.error}`, 'error');
        }
        
    } catch (error) {
        hideLoading();
        logMessage(`Crawling error: ${error.message}`, 'error');
    }
}

async function getGameDetails() {
    const packageInput = document.getElementById('packageInput');
    const packageName = packageInput.value.trim();
    
    if (!packageName) {
        logMessage('Please enter a package name', 'warn');
        packageInput.focus();
        return;
    }
    
    try {
        showLoading('Getting Game Details...', `Fetching detailed information for ${packageName}`);
        
        const result = await apiCall(`/games/${packageName}/details`, {
            method: 'POST'
        });
        
        hideLoading();
        
        if (result.success) {
            logMessage(`Game details retrieved: ${result.data.name}`, 'success');
            showGameDetails(result.data);
            
            // Refresh the games list
            await loadAllGames();
        } else {
            logMessage(`Failed to get game details: ${result.error}`, 'error');
        }
        
    } catch (error) {
        hideLoading();
        logMessage(`Get game details error: ${error.message}`, 'error');
    }
}

/**
 * Games data functions
 */
async function loadAllGames() {
    try {
        const result = await apiCall('/games');
        
        if (result.success) {
            currentGames = result.data;
            displayGames(currentGames);
            updateGamesCount(currentGames.length);
            logMessage(`Loaded ${currentGames.length} games`, 'info');
        }
        
    } catch (error) {
        logMessage(`Failed to load games: ${error.message}`, 'error');
    }
}

async function searchGames() {
    const name = document.getElementById('searchName').value.trim();
    const publisher = document.getElementById('searchPublisher').value.trim();
    
    if (!name && !publisher) {
        await loadAllGames();
        return;
    }
    
    try {
        const params = new URLSearchParams();
        if (name) params.append('name', name);
        if (publisher) params.append('publishName', publisher);
        
        const result = await apiCall(`/games/search?${params}`);
        
        if (result.success) {
            currentGames = result.data;
            displayGames(currentGames);
            updateGamesCount(currentGames.length);
            logMessage(`Search found ${currentGames.length} games`, 'info');
        }
        
    } catch (error) {
        logMessage(`Search error: ${error.message}`, 'error');
    }
}

function displayGames(games) {
    const tbody = document.getElementById('games-table-body');
    
    if (!games || games.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    No games found.
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = games.map(game => `
        <tr>
            <td>
                <strong>${escapeHtml(game.name || 'N/A')}</strong>
            </td>
            <td>
                <code>${escapeHtml(game.package || 'N/A')}</code>
            </td>
            <td>${escapeHtml(game.publishName || 'N/A')}</td>
            <td>${formatNumber(game.downloads || 0)}</td>
            <td>${formatNumber(game.dailyDownloads || 0)}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary" 
                            onclick="viewGameDetails('${escapeHtml(game.package)}')"
                            title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" 
                            onclick="refreshGameData('${escapeHtml(game.package)}')"
                            title="Refresh Data">
                        <i class="fas fa-refresh"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function updateGamesCount(count) {
    document.getElementById('games-count').textContent = `${count} games`;
}

/**
 * Game details modal
 */
function showGameDetails(gameData) {
    const content = document.getElementById('game-details-content');
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="game-detail-row">
                    <span class="game-detail-label">Name:</span>
                    <span class="game-detail-value">${escapeHtml(gameData.name || 'N/A')}</span>
                </div>
                <div class="game-detail-row">
                    <span class="game-detail-label">Package:</span>
                    <span class="game-detail-value"><code>${escapeHtml(gameData.package || 'N/A')}</code></span>
                </div>
                <div class="game-detail-row">
                    <span class="game-detail-label">Publisher:</span>
                    <span class="game-detail-value">${escapeHtml(gameData.publishName || 'N/A')}</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="game-detail-row">
                    <span class="game-detail-label">Downloads:</span>
                    <span class="game-detail-value">${formatNumber(gameData.downloads || 0)}</span>
                </div>
                <div class="game-detail-row">
                    <span class="game-detail-label">Daily Downloads:</span>
                    <span class="game-detail-value">${formatNumber(gameData.dailyDownloads || 0)}</span>
                </div>
                <div class="game-detail-row">
                    <span class="game-detail-label">Available Date:</span>
                    <span class="game-detail-value">${escapeHtml(gameData.availableDate || 'N/A')}</span>
                </div>
                <div class="game-detail-row">
                    <span class="game-detail-label">Last Update:</span>
                    <span class="game-detail-value">${escapeHtml(gameData.lastUpdateDate || 'N/A')}</span>
                </div>
            </div>
        </div>
    `;
    
    gameDetailsModal.show();
}

async function viewGameDetails(packageName) {
    try {
        const result = await apiCall(`/games/${packageName}`);
        
        if (result.success) {
            showGameDetails(result.data);
        } else {
            logMessage(`Game not found: ${packageName}`, 'warn');
        }
        
    } catch (error) {
        logMessage(`Failed to load game details: ${error.message}`, 'error');
    }
}

async function refreshGameData(packageName) {
    try {
        showLoading('Refreshing Game Data...', `Updating information for ${packageName}`);
        
        const result = await apiCall(`/games/${packageName}/details`, {
            method: 'POST'
        });
        
        hideLoading();
        
        if (result.success) {
            logMessage(`Game data refreshed: ${result.data.name}`, 'success');
            await loadAllGames(); // Refresh the table
        } else {
            logMessage(`Failed to refresh game data: ${result.error}`, 'error');
        }
        
    } catch (error) {
        hideLoading();
        logMessage(`Refresh error: ${error.message}`, 'error');
    }
}

/**
 * Statistics
 */
async function loadStatistics() {
    try {
        const result = await apiCall('/stats');
        
        if (result.success) {
            const stats = result.data;
            displayStatistics(stats);
            logMessage('Statistics loaded', 'info');
        }
        
    } catch (error) {
        logMessage(`Failed to load statistics: ${error.message}`, 'error');
    }
}

function displayStatistics(stats) {
    const content = document.getElementById('stats-content');
    
    content.innerHTML = `
        <div class="stats-item">
            <div class="stats-value">${formatNumber(stats.totalGames || 0)}</div>
            <div class="stats-label">Total Games</div>
        </div>
        <div class="stats-item">
            <div class="stats-value">${formatNumber(stats.totalDownloads || 0)}</div>
            <div class="stats-label">Total Downloads</div>
        </div>
        <div class="stats-item">
            <div class="stats-value">${formatNumber(Math.round(stats.avgDownloads || 0))}</div>
            <div class="stats-label">Avg Downloads</div>
        </div>
        <div class="stats-item">
            <div class="stats-value">${formatNumber(stats.maxDownloads || 0)}</div>
            <div class="stats-label">Max Downloads</div>
        </div>
    `;
}

/**
 * Utility functions
 */
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

function formatNumber(num) {
    if (num >= 1000000000) {
        return (num / 1000000000).toFixed(1) + 'B';
    }
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}
