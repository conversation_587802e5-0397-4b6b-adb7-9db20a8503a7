/**
 * Crawler service - Node.js equivalent of C# CrawlerData class
 */

const puppeteer = require('puppeteer');
const cheerio = require('cheerio');
const GameInfo = require('../models/GameInfo');
const { logWithTimestamp, sleep, retry, extractPublisherFromUrl } = require('../utils/utils');

class CrawlerService {
    constructor() {
        this.browser = null;
        this.page = null;
        this.isInitialized = false;
    }

    /**
     * Initialize browser and page
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            logWithTimestamp('Initializing browser...', 'info');
            
            this.browser = await puppeteer.launch({
                headless: process.env.HEADLESS === 'true',
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();
            
            // Set viewport
            await this.page.setViewport({
                width: parseInt(process.env.VIEWPORT_WIDTH) || 1920,
                height: parseInt(process.env.VIEWPORT_HEIGHT) || 1080
            });

            // Set user agent
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

            this.isInitialized = true;
            logWithTimestamp('Browser initialized successfully', 'info');
            
        } catch (error) {
            logWithTimestamp(`Browser initialization error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Navigate to URL and wait for page load
     * @param {string} url - URL to navigate to
     * @returns {Promise<void>}
     */
    async navigateToUrl(url) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            logWithTimestamp(`Navigating to: ${url}`, 'info');
            
            await this.page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: parseInt(process.env.TIMEOUT) || 30000
            });

            // Wait a bit for dynamic content to load
            await sleep(2000);
            
        } catch (error) {
            logWithTimestamp(`Navigation error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Get data from AppBrain website
     * @param {string} url - AppBrain URL
     * @returns {Promise<Array<GameInfo>>} - Array of game info objects
     */
    async getDataFromAppBrain(url) {
        try {
            await this.navigateToUrl(url);

            // Wait for content to load and try to find the rankings table
            await sleep(3000);

            // Wait for rankings table to load
            try {
                await this.page.waitForSelector('#rankings-table tbody', { timeout: 10000 });
            } catch (error) {
                logWithTimestamp('Rankings table not found, trying alternative selectors', 'warn');
            }

            // Execute script to get table content - try multiple selectors
            const tableData = await this.page.evaluate(() => {
                // First try the specific rankings table
                let rankingsTable = document.querySelector('#rankings-table tbody');
                if (rankingsTable) {
                    return { html: rankingsTable.innerHTML, type: 'rankings-tbody' };
                }

                // Try any rankings table
                rankingsTable = document.querySelector('#rankings-table');
                if (rankingsTable) {
                    return { html: rankingsTable.innerHTML, type: 'rankings-table' };
                }

                // Try general tbody
                let tbody = document.querySelector('tbody');
                if (tbody) {
                    return { html: tbody.innerHTML, type: 'tbody' };
                }

                // For ranking pages, try table with class
                let table = document.querySelector('table.table');
                if (table) {
                    return { html: table.innerHTML, type: 'table' };
                }

                // Try any table
                table = document.querySelector('table');
                if (table) {
                    return { html: table.innerHTML, type: 'table' };
                }

                // For ranking pages, try div with ranking items
                let rankingContainer = document.querySelector('.ranking-table, .ranking-list, .app-list');
                if (rankingContainer) {
                    return { html: rankingContainer.innerHTML, type: 'ranking' };
                }

                return null;
            });

            if (!tableData || !tableData.html) {
                logWithTimestamp('No table data found on AppBrain page', 'warn');
                return [];
            }

            logWithTimestamp(`Found table data using selector: ${tableData.type}`, 'info');

            // Parse HTML with Cheerio
            const $ = cheerio.load(tableData.html);
            const games = [];

            // Try different row selectors based on page type
            let rows = $('tr');
            if (rows.length === 0) {
                rows = $('.ranking-item, .app-item, .app-row');
            }

            rows.each((index, element) => {
                try {
                    const gameInfo = GameInfo.fromAppBrain($, element);
                    if (gameInfo.isValid()) {
                        games.push(gameInfo);
                    }
                } catch (error) {
                    logWithTimestamp(`Error parsing AppBrain row ${index}: ${error.message}`, 'warn');
                }
            });

            logWithTimestamp(`Extracted ${games.length} games from AppBrain`, 'info');
            return games;

        } catch (error) {
            logWithTimestamp(`AppBrain crawling error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Get data from SensorTower website
     * @param {string} url - SensorTower URL
     * @returns {Promise<Array<GameInfo>>} - Array of game info objects
     */
    async getDataFromSensorTower(url) {
        try {
            await this.navigateToUrl(url);

            // Wait for the publisher profile table to load
            await this.page.waitForSelector('#publisher-profile-table', { timeout: 10000 });

            // Execute script to get table content
            const tableHtml = await this.page.evaluate(() => {
                const table = document.getElementById('publisher-profile-table');
                return table ? table.innerHTML : null;
            });

            if (!tableHtml) {
                logWithTimestamp('No publisher profile table found on SensorTower page', 'warn');
                return [];
            }

            // Extract publisher name from URL
            const publisherName = extractPublisherFromUrl(url);

            // Parse HTML with Cheerio
            const $ = cheerio.load(tableHtml);
            const games = [];

            $('tbody tr').each((index, element) => {
                try {
                    const gameInfo = GameInfo.fromSensorTower($, element, publisherName);
                    if (gameInfo.isValid()) {
                        games.push(gameInfo);
                    }
                } catch (error) {
                    logWithTimestamp(`Error parsing SensorTower row ${index}: ${error.message}`, 'warn');
                }
            });

            logWithTimestamp(`Extracted ${games.length} games from SensorTower`, 'info');
            return games;

        } catch (error) {
            logWithTimestamp(`SensorTower crawling error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Get detailed game info from AppBrain
     * @param {string} packageName - Package name
     * @returns {Promise<GameInfo|null>} - Detailed game info or null
     */
    async getGameInfo(packageName) {
        try {
            const url = `https://www.appbrain.com/app/${packageName}`;
            await this.navigateToUrl(url);

            // Wait for page content to load
            await this.page.waitForSelector('.row', { timeout: 10000 });

            // Extract detailed information
            const gameData = await this.page.evaluate(() => {
                const rows = document.querySelectorAll('.row');
                const data = {};

                rows.forEach(row => {
                    const text = row.textContent || '';
                    
                    // Extract available date
                    if (text.includes('Available since')) {
                        const dateMatch = text.match(/Available since\s+(.+)/);
                        if (dateMatch) {
                            data.availableDate = dateMatch[1].trim();
                        }
                    }

                    // Extract last update date
                    if (text.includes('Last update')) {
                        const dateMatch = text.match(/Last update\s+(.+)/);
                        if (dateMatch) {
                            data.lastUpdateDate = dateMatch[1].trim();
                        }
                    }

                    // Extract downloads
                    if (text.includes('downloads')) {
                        const downloadMatch = text.match(/([\d,]+(?:\.\d+)?[KMB]?)\s+downloads/i);
                        if (downloadMatch) {
                            data.downloads = downloadMatch[1];
                        }
                    }

                    // Extract daily downloads
                    if (text.includes('daily downloads')) {
                        const dailyMatch = text.match(/([\d,]+(?:\.\d+)?[KMB]?)\s+daily downloads/i);
                        if (dailyMatch) {
                            data.dailyDownloads = dailyMatch[1];
                        }
                    }
                });

                // Get app name and publisher
                const titleElement = document.querySelector('h1');
                if (titleElement) {
                    data.name = titleElement.textContent.trim();
                }

                const publisherElement = document.querySelector('.app-publisher a');
                if (publisherElement) {
                    data.publishName = publisherElement.textContent.trim();
                }

                return data;
            });

            if (gameData.name) {
                const gameInfo = new GameInfo({
                    package: packageName,
                    name: gameData.name,
                    publishName: gameData.publishName || '',
                    downloads: GameInfo.convertStringToInt(gameData.downloads || '0'),
                    dailyDownloads: GameInfo.convertStringToInt(gameData.dailyDownloads || '0'),
                    availableDate: gameData.availableDate || '01/01/1970',
                    lastUpdateDate: gameData.lastUpdateDate || '01/01/1970'
                });

                logWithTimestamp(`Retrieved detailed info for: ${gameInfo.name}`, 'info');
                return gameInfo;
            }

            return null;

        } catch (error) {
            logWithTimestamp(`Get game info error for ${packageName}: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * Crawl games with retry mechanism
     * @param {string} url - URL to crawl
     * @returns {Promise<Array<GameInfo>>} - Array of game info objects
     */
    async crawlWithRetry(url) {
        return await retry(async () => {
            if (url.includes('appbrain')) {
                return await this.getDataFromAppBrain(url);
            } else if (url.includes('sensortower')) {
                return await this.getDataFromSensorTower(url);
            } else {
                throw new Error(`Unsupported URL: ${url}`);
            }
        }, 3, 2000);
    }

    /**
     * Take screenshot of current page
     * @param {string} filename - Screenshot filename
     * @returns {Promise<void>}
     */
    async takeScreenshot(filename) {
        if (!this.page) {
            throw new Error('Browser not initialized');
        }

        try {
            await this.page.screenshot({ 
                path: filename, 
                fullPage: true 
            });
            logWithTimestamp(`Screenshot saved: ${filename}`, 'info');
        } catch (error) {
            logWithTimestamp(`Screenshot error: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Close browser
     * @returns {Promise<void>}
     */
    async close() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
                this.page = null;
                this.isInitialized = false;
                logWithTimestamp('Browser closed', 'info');
            }
        } catch (error) {
            logWithTimestamp(`Browser close error: ${error.message}`, 'error');
        }
    }

    /**
     * Check if crawler is initialized
     * @returns {boolean} - True if initialized
     */
    isReady() {
        return this.isInitialized && this.browser && this.page;
    }
}

module.exports = CrawlerService;
