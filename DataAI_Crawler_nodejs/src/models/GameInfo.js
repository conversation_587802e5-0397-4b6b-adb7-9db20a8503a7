/**
 * GameInfo model - Node.js equivalent of C# GameInfo class
 */

class GameInfo {
    static Type = {
        APPBRAIN: 'appbrain',
        SENSORTOWER: 'sensortower'
    };

    constructor(data = {}) {
        this.name = data.name || '';
        this.package = data.package || '';
        this.publishName = data.publishName || '';
        this.downloads = data.downloads || 0;
        this.dailyDownloads = data.dailyDownloads || 0;
        this.availableDate = data.availableDate || '01/01/1970';
        this.lastUpdateDate = data.lastUpdateDate || '01/01/1970';
    }

    /**
     * Create GameInfo from AppBrain HTML node
     * @param {Object} $ - Cheerio instance
     * @param {Object} node - HTML node
     */
    static fromAppBrain($, node) {
        const gameInfo = new GameInfo();

        try {
            const cells = $(node).find('td');

            if (cells.length === 0) {
                return gameInfo;
            }

            // Check if this is a ranking page structure
            const rankingAppCell = $(node).find('.ranking-app-cell');
            if (rankingAppCell.length > 0) {
                // New ranking page structure

                // Extract name and package from ranking-app-cell
                const nameLink = rankingAppCell.find('a').first();
                gameInfo.name = nameLink.text().trim();

                const href = nameLink.attr('href');
                if (href) {
                    const packageMatch = href.match(/\/([^\/]+)$/);
                    if (packageMatch) {
                        gameInfo.package = packageMatch[1];
                    }
                }

                // Extract publisher name from ranking-app-cell-creator
                const publisherLink = rankingAppCell.find('.ranking-app-cell-creator a');
                gameInfo.publishName = publisherLink.text().trim();

                // Extract downloads - look for install count (usually 5th or 6th column)
                // Structure: rank, change, icon, app, rating, installs, recent
                if (cells.length >= 6) {
                    const downloadsText = $(cells[5]).text().trim(); // Installs column
                    gameInfo.downloads = this.convertStringToInt(downloadsText);
                }

                if (cells.length >= 7) {
                    const recentText = $(cells[6]).text().trim(); // Recent downloads column
                    gameInfo.dailyDownloads = this.convertStringToInt(recentText);
                }

            } else {
                // Old structure - fallback

                // Extract name and package from first cell
                const nameLink = $(cells[1]).find('a').first();
                gameInfo.name = nameLink.text().trim();

                const href = nameLink.attr('href');
                if (href) {
                    const packageMatch = href.match(/\/([^\/]+)$/);
                    if (packageMatch) {
                        gameInfo.package = packageMatch[1];
                    }
                }

                // Extract publisher name
                const publisherLink = $(cells[1]).find('.ranking-app-cell-creator a');
                gameInfo.publishName = publisherLink.text().trim();

                // Extract downloads (5th column)
                if (cells.length > 5) {
                    const downloadsText = $(cells[5]).text().trim();
                    gameInfo.downloads = this.convertStringToInt(downloadsText);
                }

                // Extract daily downloads (6th column)
                if (cells.length > 6) {
                    const dailyDownloadsText = $(cells[6]).text().trim();
                    gameInfo.dailyDownloads = Math.floor(this.convertStringToInt(dailyDownloadsText) / 30);
                }
            }

        } catch (error) {
            console.error('Error parsing AppBrain data:', error);
        }

        return gameInfo;
    }

    /**
     * Create GameInfo from SensorTower HTML node
     * @param {Object} $ - Cheerio instance
     * @param {Object} node - HTML node
     * @param {string} publishName - Publisher name from URL
     */
    static fromSensorTower($, node, publishName) {
        const gameInfo = new GameInfo();
        
        try {
            const cells = $(node).find('td');
            
            // Extract name from first cell
            gameInfo.name = $(cells[0]).text().trim();
            
            // Extract package from link if available
            const link = $(cells[0]).find('a').attr('href');
            if (link) {
                const packageMatch = link.match(/\/([^\/]+)$/);
                if (packageMatch) {
                    gameInfo.package = packageMatch[1];
                }
            }

            gameInfo.publishName = publishName;

            // Extract downloads and daily downloads based on SensorTower structure
            if (cells.length > 5) {
                const downloadsText = $(cells[5]).text().trim();
                gameInfo.downloads = this.convertStringToInt(downloadsText);
                
                const dailyDownloadsText = $(cells[6]).text().trim();
                gameInfo.dailyDownloads = Math.floor(this.convertStringToInt(dailyDownloadsText) / 30);
            }

        } catch (error) {
            console.error('Error parsing SensorTower data:', error);
        }

        return gameInfo;
    }

    /**
     * Convert string with units (K, M, B) to integer
     * @param {string} input - Input string like "1.5M", "500K"
     * @returns {number} - Converted integer value
     */
    static convertStringToInt(input) {
        if (!input || typeof input !== 'string') {
            return 0;
        }

        const cleanInput = input.toLowerCase().trim();
        
        const multipliers = {
            'k': 1000,
            'thousand': 1000,
            'm': 1000000,
            'million': 1000000,
            'b': 1000000000,
            'billion': 1000000000
        };

        for (const [unit, multiplier] of Object.entries(multipliers)) {
            if (cleanInput.includes(unit)) {
                const numberPart = cleanInput.replace(unit, '').trim();
                const number = parseFloat(numberPart);
                if (!isNaN(number)) {
                    return Math.floor(number * multiplier);
                }
            }
        }

        // Try to parse as regular integer
        const parsed = parseInt(cleanInput.replace(/[,\s]/g, ''));
        return isNaN(parsed) ? 0 : parsed;
    }

    /**
     * Get formatted result string
     * @returns {string} - Formatted game info
     */
    getResult() {
        return `Name: ${this.name}\nPackage: ${this.package}\nPublisher: ${this.publishName}\nDownloads: ${this.downloads}\nDaily Downloads: ${this.dailyDownloads}\nAvailable Date: ${this.availableDate}\nLast Update: ${this.lastUpdateDate}`;
    }

    /**
     * Convert to database insert format
     * @returns {Object} - Object ready for database insertion
     */
    toDbFormat() {
        return {
            package: this.package,
            name: this.name,
            publishName: this.publishName,
            downloads: this.downloads,
            dailyDownloads: this.dailyDownloads,
            availableDate: this.availableDate,
            lastUpdateDate: this.lastUpdateDate
        };
    }

    /**
     * Validate game info data
     * @returns {boolean} - True if valid
     */
    isValid() {
        return this.name && this.package && this.publishName;
    }
}

module.exports = GameInfo;
